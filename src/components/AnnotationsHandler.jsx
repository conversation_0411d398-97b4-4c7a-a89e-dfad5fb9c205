import { useState, useCallback } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { useHistoryManager } from './HistoryManager'

export const useAnnotationsHandler = (currentPdfIndex, currentPageIndex, customOverlapHandler = null) => {
  const [annotations, setAnnotations] = useState({}) // Annotations grouped by PDF index and page
  const [drawingMode, setDrawingMode] = useState('rectangle') // 'rectangle', 'polygon', or 'hand'
  const [currentAnnotation, setCurrentAnnotation] = useState(null)
  const [selectedAnnotations, setSelectedAnnotations] = useState([]) // Changed to array for multi-selection
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [copiedAnnotations, setCopiedAnnotations] = useState([]) // Changed to array for multi-copy
  const [polygonPoints, setPolygonPoints] = useState([])
  const [rectangleStartPoint, setRectangleStartPoint] = useState(null)

  // Scaling/resizing state
  const [isResizing, setIsResizing] = useState(false)
  const [resizeHandle, setResizeHandle] = useState(null) // 'nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'
  const [resizeStartPoint, setResizeStartPoint] = useState(null)
  const [resizeStartBounds, setResizeStartBounds] = useState(null)
  const [currentCursor, setCurrentCursor] = useState('default')

  // Initialize history manager
  const historyManager = useHistoryManager()

  // Get current PDF page key
  const getCurrentPdfPageKey = useCallback(() => {
    return `${currentPdfIndex}-${currentPageIndex}`
  }, [currentPdfIndex, currentPageIndex])

  // Get current PDF annotations for current page
  const getCurrentAnnotations = useCallback(() => {
    const key = getCurrentPdfPageKey()
    return annotations[key] || []
  }, [annotations, getCurrentPdfPageKey])

  // Update annotations for current PDF and page
  const updateCurrentAnnotations = useCallback((newAnnotations) => {
    const key = getCurrentPdfPageKey()
    setAnnotations(prev => ({
      ...prev,
      [key]: newAnnotations
    }))
  }, [getCurrentPdfPageKey])

  // Update annotations with history tracking
  const updateCurrentAnnotationsWithHistory = useCallback((newAnnotations, operation) => {
    const currentState = getCurrentAnnotations()
    const pdfPageKey = getCurrentPdfPageKey()

    // Save current state to history before making changes
    historyManager.saveToHistory(currentState, operation, pdfPageKey)

    // Apply the changes
    updateCurrentAnnotations(newAnnotations)
  }, [getCurrentAnnotations, getCurrentPdfPageKey, updateCurrentAnnotations, historyManager])

  // Multi-selection helper functions
  const isAnnotationSelected = useCallback((annotation) => {
    return selectedAnnotations.some(selected => selected.id === annotation.id)
  }, [selectedAnnotations])

  const addToSelection = useCallback((annotation) => {
    if (!isAnnotationSelected(annotation)) {
      setSelectedAnnotations(prev => [...prev, annotation])
    }
  }, [isAnnotationSelected])

  const removeFromSelection = useCallback((annotation) => {
    setSelectedAnnotations(prev => prev.filter(selected => selected.id !== annotation.id))
  }, [])

  const toggleSelection = useCallback((annotation) => {
    if (isAnnotationSelected(annotation)) {
      removeFromSelection(annotation)
    } else {
      addToSelection(annotation)
    }
  }, [isAnnotationSelected, addToSelection, removeFromSelection])

  const clearSelection = useCallback(() => {
    setSelectedAnnotations([])
  }, [])

  const selectSingle = useCallback((annotation) => {
    setSelectedAnnotations([annotation])
  }, [])

  // Get primary selected annotation (first in selection for compatibility)
  const getPrimarySelection = useCallback(() => {
    return selectedAnnotations.length > 0 ? selectedAnnotations[0] : null
  }, [selectedAnnotations])

  // Check if point is inside rectangle
  const isPointInRectangle = useCallback((point, rect) => {
    return point.x >= rect.x &&
           point.x <= rect.x + rect.width &&
           point.y >= rect.y &&
           point.y <= rect.y + rect.height
  }, [])

  // Check if point is inside polygon using ray casting algorithm
  const isPointInPolygon = useCallback((point, polygon) => {
    let inside = false
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      if (((polygon[i].y > point.y) !== (polygon[j].y > point.y)) &&
          (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
        inside = !inside
      }
    }
    return inside
  }, [])

  // Check if a point is near the first point of the current polygon (for closing)
  const isNearFirstPoint = useCallback((clickPoint, polygonPoints, threshold = 15) => {
    if (!polygonPoints || polygonPoints.length === 0) return false

    const firstPoint = polygonPoints[0]
    const distance = Math.sqrt(
      Math.pow(clickPoint.x - firstPoint.x, 2) +
      Math.pow(clickPoint.y - firstPoint.y, 2)
    )

    return distance <= threshold
  }, [])

  // Find annotation at given point
  const findAnnotationAtPoint = useCallback((point) => {
    const currentAnnotations = getCurrentAnnotations()
    for (let i = currentAnnotations.length - 1; i >= 0; i--) {
      const annotation = currentAnnotations[i]

      if (annotation.type === 'rectangle') {
        if (isPointInRectangle(point, annotation)) {
          return annotation
        }
      } else if (annotation.type === 'polygon') {
        if (isPointInPolygon(point, annotation.points)) {
          return annotation
        }
      }
    }
    return null
  }, [getCurrentAnnotations, isPointInRectangle, isPointInPolygon])

  // Edge detection utilities for resizing
  const HANDLE_SIZE = 10 // Size of resize handles
  const EDGE_THRESHOLD = 8 // Distance from edge to trigger resize cursor

  // Check if point is near a resize handle of a rectangle
  const getResizeHandle = useCallback((point, rect) => {
    const { x, y, width, height } = rect
    const handleSize = HANDLE_SIZE
    const halfHandle = handleSize / 2
    const edgeThreshold = EDGE_THRESHOLD

    // Corner handles (priority over edge handles)
    if (Math.abs(point.x - x) <= halfHandle && Math.abs(point.y - y) <= halfHandle) {
      return 'nw' // northwest
    }
    if (Math.abs(point.x - (x + width)) <= halfHandle && Math.abs(point.y - y) <= halfHandle) {
      return 'ne' // northeast
    }
    if (Math.abs(point.x - x) <= halfHandle && Math.abs(point.y - (y + height)) <= halfHandle) {
      return 'sw' // southwest
    }
    if (Math.abs(point.x - (x + width)) <= halfHandle && Math.abs(point.y - (y + height)) <= halfHandle) {
      return 'se' // southeast
    }

    // Enhanced edge detection - allow scaling from any point along the entire edge
    // North edge - anywhere along top edge (extended slightly beyond corners)
    if (Math.abs(point.y - y) <= edgeThreshold && point.x >= x - edgeThreshold && point.x <= x + width + edgeThreshold) {
      return 'n'
    }
    // South edge - anywhere along bottom edge (extended slightly beyond corners)
    if (Math.abs(point.y - (y + height)) <= edgeThreshold && point.x >= x - edgeThreshold && point.x <= x + width + edgeThreshold) {
      return 's'
    }
    // West edge - anywhere along left edge (extended slightly beyond corners)
    if (Math.abs(point.x - x) <= edgeThreshold && point.y >= y - edgeThreshold && point.y <= y + height + edgeThreshold) {
      return 'w'
    }
    // East edge - anywhere along right edge (extended slightly beyond corners)
    if (Math.abs(point.x - (x + width)) <= edgeThreshold && point.y >= y - edgeThreshold && point.y <= y + height + edgeThreshold) {
      return 'e'
    }

    return null
  }, [])

  // Get cursor style for resize handle
  const getCursorForHandle = useCallback((handle) => {
    switch (handle) {
      case 'nw': return 'nw-resize'
      case 'ne': return 'ne-resize'
      case 'sw': return 'sw-resize'
      case 'se': return 'se-resize'
      case 'n': return 'n-resize'
      case 's': return 's-resize'
      case 'w': return 'w-resize'
      case 'e': return 'e-resize'
      default: return 'default'
    }
  }, [])

  // Check if point is near a polygon vertex (for polygon scaling)
  const getPolygonVertexHandle = useCallback((point, polygon) => {
    const handleSize = HANDLE_SIZE
    const halfHandle = handleSize / 2

    for (let i = 0; i < polygon.points.length; i++) {
      const vertex = polygon.points[i]
      if (Math.abs(point.x - vertex.x) <= halfHandle && Math.abs(point.y - vertex.y) <= halfHandle) {
        return { type: 'vertex', index: i }
      }
    }
    return null
  }, [])

  // Check if point is over any resize handle of selected annotations
  const getResizeHandleAtPoint = useCallback((point) => {
    for (const annotation of selectedAnnotations) {
      if (annotation.type === 'rectangle') {
        const handle = getResizeHandle(point, annotation)
        if (handle) {
          return { annotation, handle, type: 'rectangle' }
        }
      } else if (annotation.type === 'polygon') {
        const handle = getPolygonVertexHandle(point, annotation)
        if (handle) {
          return { annotation, handle, type: 'polygon' }
        }
      }
    }
    return null
  }, [selectedAnnotations, getResizeHandle, getPolygonVertexHandle])

  // Calculate new bounds for rectangle during resize
  const calculateResizedBounds = useCallback((originalBounds, handle, currentPoint, startPoint) => {
    const { x, y, width, height } = originalBounds
    let newX = x
    let newY = y
    let newWidth = width
    let newHeight = height

    const deltaX = currentPoint.x - startPoint.x
    const deltaY = currentPoint.y - startPoint.y

    switch (handle) {
      case 'nw': // northwest corner
        newX = x + deltaX
        newY = y + deltaY
        newWidth = width - deltaX
        newHeight = height - deltaY
        break
      case 'ne': // northeast corner
        newY = y + deltaY
        newWidth = width + deltaX
        newHeight = height - deltaY
        break
      case 'sw': // southwest corner
        newX = x + deltaX
        newWidth = width - deltaX
        newHeight = height + deltaY
        break
      case 'se': // southeast corner
        newWidth = width + deltaX
        newHeight = height + deltaY
        break
      case 'n': // north edge
        newY = y + deltaY
        newHeight = height - deltaY
        break
      case 's': // south edge
        newHeight = height + deltaY
        break
      case 'w': // west edge
        newX = x + deltaX
        newWidth = width - deltaX
        break
      case 'e': // east edge
        newWidth = width + deltaX
        break
    }

    // Ensure minimum size
    const minSize = 10
    if (newWidth < minSize) {
      if (handle.includes('w')) {
        newX = x + width - minSize
      }
      newWidth = minSize
    }
    if (newHeight < minSize) {
      if (handle.includes('n')) {
        newY = y + height - minSize
      }
      newHeight = minSize
    }

    return { x: newX, y: newY, width: newWidth, height: newHeight }
  }, [])

  // Calculate new polygon points when moving a vertex
  const calculatePolygonVertexMove = useCallback((polygon, vertexIndex, newPosition) => {
    const newPoints = [...polygon.points]
    newPoints[vertexIndex] = newPosition
    return newPoints
  }, [])

  // Apply scaling transformation to multiple selected annotations
  const applyScalingToSelection = useCallback((primaryAnnotation, newBounds, originalBounds) => {
    const scaleX = newBounds.width / originalBounds.width
    const scaleY = newBounds.height / originalBounds.height

    const updatedAnnotations = getCurrentAnnotations().map(annotation => {
      const isSelected = selectedAnnotations.some(selected => selected.id === annotation.id)
      if (!isSelected) return annotation

      if (annotation.id === primaryAnnotation.id) {
        // Primary annotation gets the exact new bounds
        return { ...annotation, ...newBounds }
      } else if (annotation.type === 'rectangle') {
        // Other rectangles get scaled relative to primary
        const relativeX = annotation.x - originalBounds.x
        const relativeY = annotation.y - originalBounds.y

        return {
          ...annotation,
          x: newBounds.x + relativeX * scaleX,
          y: newBounds.y + relativeY * scaleY,
          width: annotation.width * scaleX,
          height: annotation.height * scaleY
        }
      } else if (annotation.type === 'polygon') {
        // Scale polygon points relative to primary annotation
        const scaledPoints = annotation.points.map(point => {
          const relativeX = point.x - originalBounds.x
          const relativeY = point.y - originalBounds.y

          return {
            x: newBounds.x + relativeX * scaleX,
            y: newBounds.y + relativeY * scaleY
          }
        })

        return { ...annotation, points: scaledPoints }
      }

      return annotation
    })

    return updatedAnnotations
  }, [getCurrentAnnotations, selectedAnnotations])

  // Apply polygon vertex move to selection
  const applyPolygonVertexMoveToSelection = useCallback((primaryAnnotation, vertexIndex, newPosition) => {
    const updatedAnnotations = getCurrentAnnotations().map(annotation => {
      const isSelected = selectedAnnotations.some(selected => selected.id === annotation.id)
      if (!isSelected) return annotation

      if (annotation.id === primaryAnnotation.id && annotation.type === 'polygon') {
        // Primary polygon gets the vertex moved
        const newPoints = calculatePolygonVertexMove(annotation, vertexIndex, newPosition)
        return { ...annotation, points: newPoints }
      }

      return annotation
    })

    return updatedAnnotations
  }, [getCurrentAnnotations, selectedAnnotations, calculatePolygonVertexMove])

  // Check if two rectangles overlap (with small tolerance for edge touching)
  const doRectanglesOverlap = useCallback((rect1, rect2) => {
    const tolerance = 1 // Small tolerance to avoid false positives for edge touching
    return !(rect1.x + rect1.width <= rect2.x + tolerance ||
             rect2.x + rect2.width <= rect1.x + tolerance ||
             rect1.y + rect1.height <= rect2.y + tolerance ||
             rect2.y + rect2.height <= rect1.y + tolerance)
  }, [])

  // Check if rectangle overlaps with polygon
  const doesRectangleOverlapPolygon = useCallback((rect, polygon) => {
    // Check if any corner of rectangle is inside polygon
    const corners = [
      { x: rect.x, y: rect.y },
      { x: rect.x + rect.width, y: rect.y },
      { x: rect.x, y: rect.y + rect.height },
      { x: rect.x + rect.width, y: rect.y + rect.height }
    ]

    for (const corner of corners) {
      if (isPointInPolygon(corner, polygon.points)) {
        return true
      }
    }

    // Check if any polygon point is inside rectangle
    for (const point of polygon.points) {
      if (isPointInRectangle(point, rect)) {
        return true
      }
    }

    return false
  }, [isPointInPolygon, isPointInRectangle])

  // Check if two polygons overlap (simplified check)
  const doPolygonsOverlap = useCallback((poly1, poly2) => {
    // Check if any point of poly1 is inside poly2
    for (const point of poly1.points) {
      if (isPointInPolygon(point, poly2.points)) {
        return true
      }
    }

    // Check if any point of poly2 is inside poly1
    for (const point of poly2.points) {
      if (isPointInPolygon(point, poly1.points)) {
        return true
      }
    }

    return false
  }, [isPointInPolygon])

  // Check for overlaps with existing annotations
  const checkForOverlaps = useCallback((newAnnotation) => {
    const currentAnnotations = getCurrentAnnotations()
    const overlappingAnnotations = []

    for (const existingAnnotation of currentAnnotations) {
      if (existingAnnotation.id === newAnnotation.id) continue

      let hasOverlap = false

      if (newAnnotation.type === 'rectangle' && existingAnnotation.type === 'rectangle') {
        hasOverlap = doRectanglesOverlap(newAnnotation, existingAnnotation)
      } else if (newAnnotation.type === 'rectangle' && existingAnnotation.type === 'polygon') {
        hasOverlap = doesRectangleOverlapPolygon(newAnnotation, existingAnnotation)
      } else if (newAnnotation.type === 'polygon' && existingAnnotation.type === 'rectangle') {
        hasOverlap = doesRectangleOverlapPolygon(existingAnnotation, newAnnotation)
      } else if (newAnnotation.type === 'polygon' && existingAnnotation.type === 'polygon') {
        hasOverlap = doPolygonsOverlap(newAnnotation, existingAnnotation)
      }

      if (hasOverlap) {
        overlappingAnnotations.push(existingAnnotation)
      }
    }

    return overlappingAnnotations
  }, [getCurrentAnnotations, doRectanglesOverlap, doesRectangleOverlapPolygon, doPolygonsOverlap])

  // Create rectangle annotation (returns annotation without adding to list)
  const createRectangleAnnotation = useCallback((startPoint, endPoint, onAnnotationCreated) => {
    const startX = Math.min(startPoint.x, endPoint.x)
    const startY = Math.min(startPoint.y, endPoint.y)
    const width = Math.abs(endPoint.x - startPoint.x)
    const height = Math.abs(endPoint.y - startPoint.y)

    // Only create rectangle if it has meaningful dimensions
    if (width > 5 && height > 5) {
      const currentAnnotations = getCurrentAnnotations()
      const annotationNumber = currentAnnotations.length + 1
      const newAnnotation = {
        id: uuidv4(),
        type: 'rectangle',
        pageIndex: currentPageIndex,
        x: startX,
        y: startY,
        width: width,
        height: height,
        color: '#ff0000',
        label: `Rectangle ${annotationNumber}`
      }

      // Check for overlaps
      const overlappingAnnotations = checkForOverlaps(newAnnotation)
      if (overlappingAnnotations.length > 0) {
        const overlappingLabels = overlappingAnnotations.map(ann => ann.label || `${ann.type} ${ann.id.slice(0, 8)}`).join(', ')

        if (customOverlapHandler) {
          // Use custom overlap handler (toast-based)
          customOverlapHandler(
            overlappingLabels,
            () => {
              // Proceed with annotation creation
              if (onAnnotationCreated) {
                onAnnotationCreated(newAnnotation)
              } else {
                updateCurrentAnnotations([...currentAnnotations, newAnnotation])
              }
            },
            () => {
              // Cancel annotation creation
              return null
            }
          )
          return null // Don't proceed immediately, wait for user choice
        } else {
          // Fallback to window.confirm
          const proceed = window.confirm(
            `Warning: This rectangle overlaps with existing annotation(s): ${overlappingLabels}\n\n` +
            `In this application, no area should be shared by more than one annotation. ` +
            `Do you want to create this overlapping annotation anyway?`
          )

          if (!proceed) {
            return null
          }
        }
      }

      // If callback provided, use it (for room dropdown), otherwise add directly
      if (onAnnotationCreated) {
        onAnnotationCreated(newAnnotation)
      } else {
        updateCurrentAnnotationsWithHistory([...currentAnnotations, newAnnotation], 'Create Rectangle')
      }
      return newAnnotation
    }
    return null
  }, [currentPageIndex, getCurrentAnnotations, updateCurrentAnnotationsWithHistory, checkForOverlaps])

  // Finish polygon annotation
  const finishPolygon = useCallback((onAnnotationCreated) => {
    if (currentAnnotation && polygonPoints.length >= 3) {
      // Check for overlaps
      const overlappingAnnotations = checkForOverlaps(currentAnnotation)
      if (overlappingAnnotations.length > 0) {
        const overlappingLabels = overlappingAnnotations.map(ann => ann.label || `${ann.type} ${ann.id.slice(0, 8)}`).join(', ')

        if (customOverlapHandler) {
          // Use custom overlap handler (toast-based)
          customOverlapHandler(
            overlappingLabels,
            () => {
              // Proceed with annotation creation
              if (onAnnotationCreated) {
                onAnnotationCreated(currentAnnotation)
              } else {
                updateCurrentAnnotations([...getCurrentAnnotations(), currentAnnotation])
              }
              setCurrentAnnotation(null)
              setPolygonPoints([])
            },
            () => {
              // Cancel annotation creation
              setCurrentAnnotation(null)
              setPolygonPoints([])
            }
          )
          return false // Don't proceed immediately, wait for user choice
        } else {
          // Fallback to window.confirm
          const proceed = window.confirm(
            `Warning: This polygon overlaps with existing annotation(s): ${overlappingLabels}\n\n` +
            `In this application, no area should be shared by more than one annotation. ` +
            `Do you want to create this overlapping annotation anyway?`
          )

          if (!proceed) {
            setCurrentAnnotation(null)
            setPolygonPoints([])
            return false
          }
        }
      }

      // If callback provided, use it (for room dropdown), otherwise add directly
      if (onAnnotationCreated) {
        onAnnotationCreated(currentAnnotation)
      } else {
        updateCurrentAnnotationsWithHistory([...getCurrentAnnotations(), currentAnnotation], 'Create Polygon')
      }

      setCurrentAnnotation(null)
      setPolygonPoints([])
      return true
    }
    return false
  }, [currentAnnotation, polygonPoints, getCurrentAnnotations, updateCurrentAnnotationsWithHistory, checkForOverlaps])

  // Copy annotation functionality - now supports multiple annotations
  const copyAnnotation = useCallback((annotation) => {
    if (annotation) {
      // Single annotation copy
      setCopiedAnnotations([{ ...annotation, id: uuidv4() }])
    } else if (selectedAnnotations.length > 0) {
      // Copy all selected annotations
      setCopiedAnnotations(selectedAnnotations.map(ann => ({ ...ann, id: uuidv4() })))
    }
  }, [selectedAnnotations])

  const copySelectedAnnotations = useCallback(() => {
    if (selectedAnnotations.length > 0) {
      setCopiedAnnotations(selectedAnnotations.map(ann => ({ ...ann, id: uuidv4() })))
    }
  }, [selectedAnnotations])

  // Delete annotation - now supports multiple annotations
  const deleteAnnotation = useCallback((annotationId) => {
    try {
      const currentAnnotations = getCurrentAnnotations()
      if (!Array.isArray(currentAnnotations)) {
        console.error('getCurrentAnnotations() did not return an array:', currentAnnotations)
        return
      }

      const updatedAnnotations = currentAnnotations.filter(ann => ann.id !== annotationId)
      updateCurrentAnnotationsWithHistory(updatedAnnotations, 'Delete Annotation')

      // Remove from selection if it was selected
      setSelectedAnnotations(prev => prev.filter(selected => selected.id !== annotationId))
    } catch (error) {
      console.error('Error deleting annotation:', error)
    }
  }, [getCurrentAnnotations, updateCurrentAnnotationsWithHistory])

  const deleteSelectedAnnotations = useCallback(() => {
    try {
      const currentAnnotations = getCurrentAnnotations()
      if (!Array.isArray(currentAnnotations)) {
        console.error('getCurrentAnnotations() did not return an array:', currentAnnotations)
        return
      }

      const selectedIds = new Set(selectedAnnotations.map(ann => ann.id))
      const updatedAnnotations = currentAnnotations.filter(ann => !selectedIds.has(ann.id))
      const operationName = selectedAnnotations.length === 1 ? 'Delete Annotation' : `Delete ${selectedAnnotations.length} Annotations`
      updateCurrentAnnotationsWithHistory(updatedAnnotations, operationName)
      clearSelection()
    } catch (error) {
      console.error('Error deleting selected annotations:', error)
    }
  }, [getCurrentAnnotations, updateCurrentAnnotationsWithHistory, selectedAnnotations, clearSelection])

  // Update annotation label
  const updateAnnotationLabel = useCallback((annotationId, newLabel) => {
    const currentAnnotations = getCurrentAnnotations()
    const updatedAnnotations = currentAnnotations.map(annotation =>
      annotation.id === annotationId
        ? { ...annotation, label: newLabel }
        : annotation
    )
    updateCurrentAnnotationsWithHistory(updatedAnnotations, 'Update Label')

    // Update selected annotations if any match the edited one
    setSelectedAnnotations(prev => prev.map(selected =>
      selected.id === annotationId
        ? { ...selected, label: newLabel }
        : selected
    ))
  }, [getCurrentAnnotations, updateCurrentAnnotationsWithHistory])

  // Clear current drawing state
  const clearDrawingState = useCallback(() => {
    setCurrentAnnotation(null)
    setPolygonPoints([])
    setRectangleStartPoint(null)
    clearSelection()
    setIsDragging(false)
  }, [clearSelection])

  // Undo/Redo functions
  const undo = useCallback(() => {
    const pdfPageKey = getCurrentPdfPageKey()
    return historyManager.undo(
      getCurrentAnnotations,
      (restoredState) => {
        updateCurrentAnnotations(restoredState)
        // Clear selection when undoing to avoid inconsistencies
        setSelectedAnnotations([])
      },
      pdfPageKey
    )
  }, [historyManager, getCurrentPdfPageKey, getCurrentAnnotations, updateCurrentAnnotations])

  const redo = useCallback(() => {
    const pdfPageKey = getCurrentPdfPageKey()
    return historyManager.redo(
      getCurrentAnnotations,
      (restoredState) => {
        updateCurrentAnnotations(restoredState)
        // Clear selection when redoing to avoid inconsistencies
        setSelectedAnnotations([])
      },
      pdfPageKey
    )
  }, [historyManager, getCurrentPdfPageKey, getCurrentAnnotations, updateCurrentAnnotations])

  // History status functions
  const canUndo = useCallback(() => {
    const pdfPageKey = getCurrentPdfPageKey()
    return historyManager.canUndo(pdfPageKey)
  }, [historyManager, getCurrentPdfPageKey])

  const canRedo = useCallback(() => {
    const pdfPageKey = getCurrentPdfPageKey()
    return historyManager.canRedo(pdfPageKey)
  }, [historyManager, getCurrentPdfPageKey])

  const getLastUndoOperation = useCallback(() => {
    const pdfPageKey = getCurrentPdfPageKey()
    return historyManager.getLastUndoOperation(pdfPageKey)
  }, [historyManager, getCurrentPdfPageKey])

  const getLastRedoOperation = useCallback(() => {
    const pdfPageKey = getCurrentPdfPageKey()
    return historyManager.getLastRedoOperation(pdfPageKey)
  }, [historyManager, getCurrentPdfPageKey])

  // Special function to save movement history with pre-movement state
  const saveMovementToHistory = useCallback((preMovementState, operation) => {
    const pdfPageKey = getCurrentPdfPageKey()
    historyManager.saveToHistory(preMovementState, operation, pdfPageKey)
  }, [historyManager, getCurrentPdfPageKey])

  return {
    // State
    annotations,
    drawingMode,
    currentAnnotation,
    selectedAnnotations, // Changed from selectedAnnotation
    isDragging,
    dragOffset,
    copiedAnnotations, // Changed from copiedAnnotation
    polygonPoints,
    rectangleStartPoint,

    // Scaling/resizing state
    isResizing,
    resizeHandle,
    resizeStartPoint,
    resizeStartBounds,
    currentCursor,

    // Actions
    setDrawingMode,
    setCurrentAnnotation,
    setSelectedAnnotations, // Changed from setSelectedAnnotation
    setIsDragging,
    setDragOffset,
    setCopiedAnnotations, // Changed from setCopiedAnnotation
    setPolygonPoints,
    setRectangleStartPoint,
    getCurrentAnnotations,
    updateCurrentAnnotations,
    findAnnotationAtPoint,
    createRectangleAnnotation,
    finishPolygon,
    copyAnnotation,
    copySelectedAnnotations,
    deleteAnnotation,
    deleteSelectedAnnotations,
    updateAnnotationLabel,
    checkForOverlaps,
    clearDrawingState,

    // Scaling/resizing actions
    setIsResizing,
    setResizeHandle,
    setResizeStartPoint,
    setResizeStartBounds,
    setCurrentCursor,

    // Multi-selection helpers
    isAnnotationSelected,
    addToSelection,
    removeFromSelection,
    toggleSelection,
    clearSelection,
    selectSingle,
    getPrimarySelection,

    // Resize/scaling utilities
    getResizeHandle,
    getPolygonVertexHandle,
    getCursorForHandle,
    getResizeHandleAtPoint,
    calculateResizedBounds,
    calculatePolygonVertexMove,
    applyScalingToSelection,
    applyPolygonVertexMoveToSelection,

    // Polygon utilities
    isNearFirstPoint,

    // Undo/Redo functionality
    undo,
    redo,
    canUndo,
    canRedo,
    getLastUndoOperation,
    getLastRedoOperation,
    saveMovementToHistory,
    clearHistory: historyManager.clearHistory,
    clearHistoryForPage: historyManager.clearHistoryForPage
  }
}
